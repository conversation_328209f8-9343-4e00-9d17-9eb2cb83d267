@echo off
chcp 65001 >nul
title NAFOODLVN Docker Helper

:menu
cls
echo.
echo 🐳 ================================
echo    NAFOODLVN DOCKER HELPER
echo ================================
echo.
echo 🚀 CHỌN HÀNH ĐỘNG:
echo.
echo [1] ▶️  Chạy Docker (Start)
echo [2] 🔨 Build + Chạy Docker
echo [3] ⏹️  Dừng Docker (Stop)
echo [4] 🔄 Restart Docker
echo [5] 📊 Xem trạng thái containers
echo [6] 📝 Xem logs
echo [7] 🔍 Test API Health
echo [8] 🌱 Seed dữ liệu mẫu
echo [9] 💾 Backup database
echo [0] 🧹 Reset hoàn toàn
echo [Q] ❌ Thoát
echo.
set /p choice="👉 Nhập lựa chọn (1-9, 0, Q): "

if /i "%choice%"=="1" goto start
if /i "%choice%"=="2" goto build
if /i "%choice%"=="3" goto stop
if /i "%choice%"=="4" goto restart
if /i "%choice%"=="5" goto status
if /i "%choice%"=="6" goto logs
if /i "%choice%"=="7" goto health
if /i "%choice%"=="8" goto seed
if /i "%choice%"=="9" goto backup
if /i "%choice%"=="0" goto reset
if /i "%choice%"=="q" goto exit

echo ❌ Lựa chọn không hợp lệ!
timeout /t 2 >nul
goto menu

:start
echo.
echo 🚀 Đang khởi động Docker containers...
docker-compose up -d
if %errorlevel% equ 0 (
    echo ✅ Khởi động thành công!
    echo 🔗 Truy cập: http://localhost:3000/api/health
) else (
    echo ❌ Khởi động thất bại!
)
pause
goto menu

:build
echo.
echo 🔨 Đang build và khởi động Docker containers...
docker-compose up --build -d
if %errorlevel% equ 0 (
    echo ✅ Build và khởi động thành công!
    echo 🔗 Truy cập: http://localhost:3000/api/health
) else (
    echo ❌ Build thất bại!
)
pause
goto menu

:stop
echo.
echo ⏹️ Đang dừng Docker containers...
docker-compose down
if %errorlevel% equ 0 (
    echo ✅ Dừng thành công!
) else (
    echo ❌ Dừng thất bại!
)
pause
goto menu

:restart
echo.
echo 🔄 Đang restart Docker containers...
docker-compose restart
if %errorlevel% equ 0 (
    echo ✅ Restart thành công!
) else (
    echo ❌ Restart thất bại!
)
pause
goto menu

:status
echo.
echo 📊 Trạng thái Docker containers:
echo ================================
docker-compose ps
echo.
echo 📈 Resource usage:
echo ================================
docker stats --no-stream
pause
goto menu

:logs
echo.
echo 📝 Docker logs (Nhấn Ctrl+C để thoát):
echo ========================================
docker-compose logs -f
pause
goto menu

:health
echo.
echo 🔍 Đang kiểm tra API Health...
echo ==============================
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/api/health' -UseBasicParsing; Write-Host '✅ API Health: OK'; Write-Host $response.Content } catch { Write-Host '❌ API Health: FAILED'; Write-Host $_.Exception.Message }"
pause
goto menu

:seed
echo.
echo 🌱 Đang seed dữ liệu mẫu...
echo ===========================
docker-compose exec backend node scripts/seed-data.js
if %errorlevel% equ 0 (
    echo ✅ Seed dữ liệu thành công!
) else (
    echo ❌ Seed dữ liệu thất bại!
)
pause
goto menu

:backup
echo.
echo 💾 Đang backup database...
echo ==========================
set backup_name=nafood_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set backup_name=%backup_name: =0%
echo 📁 Backup name: %backup_name%

docker exec nafood-mongodb mongodump --uri="*******************************************************************" --out="/tmp/%backup_name%"
if %errorlevel% equ 0 (
    if not exist "backups" mkdir backups
    docker cp nafood-mongodb:/tmp/%backup_name% ./backups/
    if %errorlevel% equ 0 (
        echo ✅ Backup thành công!
        echo 📁 Backup saved to: ./backups/%backup_name%
    ) else (
        echo ❌ Copy backup thất bại!
    )
) else (
    echo ❌ Backup thất bại!
)
pause
goto menu

:reset
echo.
echo 🧹 ⚠️  CẢNH BÁO: Thao tác này sẽ XÓA TẤT CẢ DỮ LIỆU!
echo ================================================
set /p confirm="👉 Bạn có chắc chắn? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo.
echo 🧹 Đang reset hoàn toàn...
echo ==========================
echo 1️⃣ Dừng containers...
docker-compose down -v

echo 2️⃣ Dọn dẹp Docker system...
docker system prune -a -f

echo 3️⃣ Khởi động lại...
docker-compose up --build -d

if %errorlevel% equ 0 (
    echo ✅ Reset hoàn toàn thành công!
    echo 🔗 Truy cập: http://localhost:3000/api/health
) else (
    echo ❌ Reset thất bại!
)
pause
goto menu

:exit
echo.
echo 👋 Cảm ơn bạn đã sử dụng NAFOODLVN Docker Helper!
echo 🚀 Happy Coding!
timeout /t 2 >nul
exit

:error
echo ❌ Đã xảy ra lỗi! Vui lòng thử lại.
pause
goto menu
