#!/usr/bin/env node

/**
 * Docker Health Check Script
 * This script performs comprehensive health checks for the Docker environment
 */

import http from 'http';
import { MongoClient } from 'mongodb';

const MONGODB_URI = process.env.MONGODB_URI || '*****************************************************************';
const PORT = process.env.PORT || 3000;
const HOST = 'localhost';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Check HTTP server health
function checkHttpServer() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: HOST,
      port: PORT,
      path: '/api/health',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (res.statusCode === 200 && response.status === 'healthy') {
            resolve({ status: 'healthy', data: response });
          } else {
            reject(new Error(`HTTP health check failed: ${res.statusCode} - ${data}`));
          }
        } catch (error) {
          reject(new Error(`Invalid JSON response: ${data}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`HTTP request failed: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('HTTP request timeout'));
    });

    req.end();
  });
}

// Check MongoDB connection
async function checkMongoDB() {
  let client;
  try {
    client = new MongoClient(MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 5000,
    });

    await client.connect();
    
    // Test database operations
    const db = client.db('nafood');
    await db.admin().ping();
    
    // Check if collections exist
    const collections = await db.listCollections().toArray();
    const expectedCollections = ['users', 'products', 'orders', 'reviews', 'banners', 'counters'];
    const existingCollections = collections.map(c => c.name);
    
    const missingCollections = expectedCollections.filter(c => !existingCollections.includes(c));
    
    return {
      status: 'healthy',
      collections: existingCollections.length,
      missingCollections: missingCollections.length > 0 ? missingCollections : null
    };
  } catch (error) {
    throw new Error(`MongoDB connection failed: ${error.message}`);
  } finally {
    if (client) {
      await client.close();
    }
  }
}

// Check environment variables
function checkEnvironment() {
  const requiredEnvVars = [
    'MONGODB_URI',
    'JWT_SECRET',
    'SESSION_SECRET'
  ];

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  return {
    status: 'healthy',
    nodeEnv: process.env.NODE_ENV,
    dockerEnv: process.env.DOCKER_ENV,
    port: process.env.PORT
  };
}

// Main health check function
async function performHealthCheck() {
  log('🔍 Starting Docker Health Check...', colors.blue);
  log('================================', colors.blue);

  const results = {
    timestamp: new Date().toISOString(),
    checks: {}
  };

  // Check Environment Variables
  try {
    log('📋 Checking environment variables...', colors.yellow);
    results.checks.environment = await checkEnvironment();
    log('✅ Environment variables: OK', colors.green);
  } catch (error) {
    log(`❌ Environment variables: ${error.message}`, colors.red);
    results.checks.environment = { status: 'unhealthy', error: error.message };
  }

  // Check MongoDB
  try {
    log('🗄️  Checking MongoDB connection...', colors.yellow);
    results.checks.mongodb = await checkMongoDB();
    log('✅ MongoDB: OK', colors.green);
    if (results.checks.mongodb.missingCollections) {
      log(`⚠️  Missing collections: ${results.checks.mongodb.missingCollections.join(', ')}`, colors.yellow);
    }
  } catch (error) {
    log(`❌ MongoDB: ${error.message}`, colors.red);
    results.checks.mongodb = { status: 'unhealthy', error: error.message };
  }

  // Check HTTP Server
  try {
    log('🌐 Checking HTTP server...', colors.yellow);
    results.checks.http = await checkHttpServer();
    log('✅ HTTP server: OK', colors.green);
  } catch (error) {
    log(`❌ HTTP server: ${error.message}`, colors.red);
    results.checks.http = { status: 'unhealthy', error: error.message };
  }

  // Overall health status
  const allHealthy = Object.values(results.checks).every(check => check.status === 'healthy');
  results.overall = allHealthy ? 'healthy' : 'unhealthy';

  log('================================', colors.blue);
  if (allHealthy) {
    log('🎉 All health checks passed!', colors.green);
    process.exit(0);
  } else {
    log('💥 Some health checks failed!', colors.red);
    console.log('\nDetailed results:');
    console.log(JSON.stringify(results, null, 2));
    process.exit(1);
  }
}

// Run health check if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  performHealthCheck().catch((error) => {
    log(`💥 Health check failed: ${error.message}`, colors.red);
    process.exit(1);
  });
}

export { performHealthCheck, checkHttpServer, checkMongoDB, checkEnvironment };
