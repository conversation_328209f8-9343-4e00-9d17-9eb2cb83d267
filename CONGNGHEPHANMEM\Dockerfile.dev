# Development Dockerfile with hot reload
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install nodemon globally for hot reload
RUN npm install -g nodemon

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Create uploads directory
RUN mkdir -p public/uploads

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nafood -u 1001

# Change ownership
RUN chown -R nafood:nodejs /app
USER nafood

# Expose port
EXPOSE 3000

# Start with nodemon for hot reload
CMD ["npm", "run", "dev"]
