/* Admin Panel Custom Styles */

/* Sidebar Animation */
.admin-sidebar {
  transition: all 0.3s ease-in-out;
}

.admin-sidebar:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Navigation Button Hover Effects */
.admin-nav-button {
  position: relative;
  overflow: hidden;
}

.admin-nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.admin-nav-button:hover::before {
  left: 100%;
}

/* Card Hover Effects */
.admin-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.admin-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.admin-card:hover::before {
  transform: scaleX(1);
}

.admin-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Stats Card Animations */
.stats-card {
  animation: fadeInUp 0.6s ease-out;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Icon Pulse Animation */
.icon-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading Skeleton */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Custom Scrollbar */
.admin-content::-webkit-scrollbar {
  width: 6px;
}

.admin-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.admin-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 3px;
}

.admin-content::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* Fix Admin Layout and Scrolling Issues */
.admin-layout {
  height: 100vh;
  overflow: hidden;
}

.admin-main-content {
  height: calc(100vh - 4rem); /* Subtract header height */
  overflow-y: auto;
  overflow-x: hidden;
}

.admin-sidebar {
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.admin-page-container {
  min-height: calc(100vh - 8rem);
  padding: 1rem 3rem 1.5rem 2rem;
  margin-left: 1rem;
}

/* Ensure tables and forms are responsive */
.admin-table-container {
  overflow-x: auto;
  max-width: 100%;
}

.admin-form-container {
  max-width: 100%;
  overflow-x: hidden;
}

/* Dashboard content spacing */
.admin-main-content [data-state="active"] {
  padding-right: 1rem;
}

/* Stats cards styling */
.stats-card {
  transition: all 0.3s ease;
  position: relative;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  right: -4px;
  bottom: -4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  z-index: -1;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.stats-card:hover::before {
  top: 6px;
  left: 6px;
  right: -6px;
  bottom: -6px;
  background: rgba(0, 0, 0, 0.15);
}

/* Stats container background layer */
.stats-container {
  position: relative;
}

.stats-container::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 8px;
  right: -8px;
  bottom: -8px;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(16, 185, 129, 0.05) 25%,
    rgba(139, 92, 246, 0.05) 50%,
    rgba(239, 68, 68, 0.05) 100%);
  border-radius: 16px;
  z-index: -1;
  opacity: 0.7;
}

/* Compact filter styling - now using simple div instead of Card */
.admin-filter-section {
  padding: 0.5rem 0.75rem;
}

.admin-filter-section .filter-input {
  height: 1.75rem;
  font-size: 0.75rem;
}

.admin-filter-section .filter-select {
  height: 1.75rem;
  font-size: 0.75rem;
}

.admin-filter-section .filter-button {
  height: 1.75rem;
  padding: 0 0.5rem;
  font-size: 0.75rem;
}

/* Compact labels */
.admin-filter-section label {
  font-size: 0.7rem;
  font-weight: 500;
  margin-bottom: 0.125rem;
}

/* New compact filter bar styling */
.bg-gray-50.border.border-gray-200.rounded-lg {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.bg-gray-50.border.border-gray-200.rounded-lg input,
.bg-gray-50.border.border-gray-200.rounded-lg button[role="combobox"] {
  transition: all 0.2s ease;
}

.bg-gray-50.border.border-gray-200.rounded-lg input:focus,
.bg-gray-50.border.border-gray-200.rounded-lg button[role="combobox"]:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Fix modal and dialog positioning */
.admin-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  overflow-y: auto;
}

.admin-modal-content {
  max-height: 85vh;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Ensure dialog content is scrollable */
[data-radix-dialog-content] {
  max-height: 85vh !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* Fix dialog overlay to allow scrolling */
[data-radix-dialog-overlay] {
  overflow-y: auto !important;
}

/* Ensure form content inside dialog can scroll */
.admin-modal-content form {
  max-height: none;
  overflow: visible;
}

/* Fix for specific dialog content */
.max-w-2xl.admin-modal-content {
  max-height: 85vh;
  overflow-y: auto;
  padding: 1.5rem;
}

/* Simple dialog scrolling fix - just add scroll to content */
[data-radix-dialog-content] {
  max-height: 90vh !important;
  overflow-y: auto !important;
}

/* Specific fix for admin modal content */
.admin-modal-content {
  max-height: calc(100vh - 4rem) !important;
  overflow-y: auto !important;
}

/* Override Radix UI default positioning */
[data-radix-dialog-content][data-state="open"] {
  animation: none !important;
  position: relative !important;
  top: auto !important;
  left: auto !important;
  transform: none !important;
}

/* Ensure body doesn't prevent scrolling */
body:has([data-radix-dialog-overlay]) {
  overflow: hidden !important;
}

[data-radix-dialog-overlay] {
  overflow-y: scroll !important;
}

/* Force scroll on all dialog content */
[data-radix-dialog-content] {
  max-height: 90vh !important;
  overflow-y: auto !important;
}

/* Specific override for product form */
.admin-modal-content {
  max-height: 85vh !important;
  overflow-y: auto !important;
}

/* ===== BEAUTIFUL HEADER & NAVIGATION STYLES ===== */

/* Enhanced Header Styling */
.main-header {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff3838 100%);
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.main-header:hover {
  box-shadow: 0 12px 40px rgba(255, 107, 107, 0.4);
}

/* Logo Enhancement */
.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  transition: transform 0.3s ease;
}

.logo-container:hover {
  transform: scale(1.05);
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.logo-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.logo-icon:hover::before {
  opacity: 1;
  animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.logo-text {
  font-family: 'Lobster', cursive;
  font-size: 28px;
  font-weight: 700;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

/* Navigation Enhancement */
.main-nav {
  display: flex;
  align-items: center;
  gap: 32px;
  padding: 16px 0;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  padding: 12px 20px;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Search Bar Enhancement */
.search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
  margin: 0 32px;
}

.search-input {
  width: 100%;
  padding: 14px 50px 14px 20px;
  border: none;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
  outline: none;
  background: white;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.search-input::placeholder {
  color: #666;
  font-style: italic;
}

.search-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  transition: color 0.3s ease;
}

.search-container:hover .search-icon {
  color: #ff6b6b;
}

/* User Actions Enhancement */
.user-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.cart-icon, .user-icon {
  position: relative;
  padding: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
}

.cart-icon:hover, .user-icon:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.cart-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff3838;
  color: white;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.user-name {
  color: white;
  font-weight: 600;
  font-size: 16px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* ===== BEAUTIFUL BANNER SECTION ===== */

.hero-banner {
  position: relative;
  height: 400px;
  border-radius: 20px;
  overflow: hidden;
  margin: 20px 0;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.hero-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.1) 100%);
  z-index: 2;
}

.hero-content {
  position: absolute;
  top: 50%;
  left: 60px;
  transform: translateY(-50%);
  z-index: 3;
  color: white;
}

.hero-title {
  font-size: 48px;
  font-weight: 800;
  margin-bottom: 16px;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 18px;
  margin-bottom: 32px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  opacity: 0.95;
}

.hero-cta {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 16px 32px;
  border: none;
  border-radius: 50px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
  position: relative;
  overflow: hidden;
}

.hero-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.hero-cta:hover::before {
  left: 100%;
}

.hero-cta:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.5);
}

/* ===== BEAUTIFUL CATEGORY MENU ===== */

.category-section {
  margin: 40px 0;
}

.section-title {
  text-align: center;
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  text-align: center;
  font-size: 18px;
  color: #666;
  margin-bottom: 40px;
  font-style: italic;
}

.category-filters {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.category-btn {
  padding: 12px 24px;
  border: 2px solid #e0e0e0;
  background: white;
  color: #666;
  border-radius: 50px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.category-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  transition: left 0.3s ease;
  z-index: -1;
}

.category-btn:hover::before,
.category-btn.active::before {
  left: 0;
}

.category-btn:hover,
.category-btn.active {
  color: white;
  border-color: #ff6b6b;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

/* Sort Controls Enhancement */
.sort-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
  justify-content: flex-end;
}

.sort-select {
  padding: 10px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sort-select:focus {
  outline: none;
  border-color: #ff6b6b;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.2);
}

/* Compact admin components */
.admin-form-container {
  max-width: 100%;
  overflow-x: hidden;
}

.admin-form-container h2 {
  margin-bottom: 0.25rem;
}

.admin-form-container p {
  margin-bottom: 0.5rem;
}

/* Compact stats cards */
.admin-stats-compact {
  padding: 0.75rem;
}

.admin-stats-compact .stat-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.admin-stats-compact .stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.2;
}

/* Compact filter sections */
.admin-filter-compact {
  padding: 0.75rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.admin-filter-compact .filter-grid {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.admin-filter-compact .filter-item {
  flex: 1;
  min-width: 150px;
}

.admin-filter-compact label {
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  display: block;
}

.admin-filter-compact input,
.admin-filter-compact button[role="combobox"] {
  height: 2rem;
  font-size: 0.875rem;
}

.admin-filter-compact .filter-button {
  height: 2rem;
  padding: 0 0.75rem;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
    position: fixed;
    z-index: 50;
  }
  
  .admin-sidebar.open {
    transform: translateX(0);
  }

  .admin-page-container {
    padding: 1rem;
    margin-left: 0;
  }

  .admin-main-content [data-state="active"] {
    padding-right: 0;
  }
}

/* Button Ripple Effect */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::after {
  width: 300px;
  height: 300px;
}
