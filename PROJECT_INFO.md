# 📋 Thông tin dự án

## 🏪 HT Cửa Hàng Bán Đồ Ăn

### 📊 Thống kê dự án
- **Phiên bản hiện tại**: 2.0.0
- **<PERSON><PERSON><PERSON> cập nhật**: 24/07/2025
- **<PERSON><PERSON><PERSON>**: <PERSON><PERSON>
- **Email**: <EMAIL>
- **Repository**: https://github.com/Lamvanna/HTCUAHANGBANDOAN.git

### 🛠️ Công nghệ sử dụng
- **Frontend**: React 18, Tailwind CSS, Vite
- **Backend**: Node.js, Express.js, MongoDB
- **Authentication**: JWT, Bcrypt
- **Deployment**: Docker, PM2

### 📈 Tính năng chính
1. **Quản lý sản phẩm**: <PERSON>h<PERSON><PERSON>, sửa, xóa món ăn
2. **Quản lý đơn hàng**: <PERSON> dõi và xử lý đơn hàng
3. **<PERSON><PERSON> thống người dùng**: <PERSON><PERSON>, Staff, Customer
4. **Thanh toán**: <PERSON><PERSON> <PERSON>hương thức thanh toán
5. **Báo cáo**: Thống kê doanh thu và hiệu suất

### 🎯 Mục tiêu phát triển
- [ ] Thêm tính năng chat real-time
- [ ] Tích hợp payment gateway
- [ ] Mobile app với React Native
- [ ] AI recommendation system
- [ ] Multi-language support

### 📞 Liên hệ hỗ trợ
- **GitHub Issues**: [Báo lỗi tại đây](https://github.com/Lamvanna/HTCUAHANGBANDOAN/issues)
- **Email**: <EMAIL>
- **Documentation**: Xem README.md để biết thêm chi tiết

---
*Cập nhật lần cuối: 24/07/2025*
