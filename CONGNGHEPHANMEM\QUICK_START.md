# ⚡ **QUICK START - NAFOODLVN DOCKER**

## 🚀 **CHẠY NHANH (5 PHÚT)**

### **Bước 1: <PERSON><PERSON><PERSON> bị**
```bash
cd CONGNGHEPHANMEM
copy .env.example .env    # Windows
# cp .env.example .env    # Linux/macOS
```

### **Bước 2: Ch<PERSON><PERSON> Docker**
```bash
docker-compose up --build -d
```

### **Bước 3: Ki<PERSON><PERSON> tra**
```bash
# Xem trạng thái
docker-compose ps

# Test API
curl http://localhost:3000/api/health
# Hoặc mở browser: http://localhost:3000/api/health
```

### **Bước 4: Seed dữ liệu (tùy chọn)**
```bash
docker-compose exec backend node scripts/seed-data.js
```

---

## 🔗 **TRUY CẬP**

- **🏠 Trang chủ**: http://localhost:3000
- **🔍 Health Check**: http://localhost:3000/api/health
- **📦 Products API**: http://localhost:3000/api/products
- **🎨 Banners API**: http://localhost:3000/api/banners

---

## 📋 **LỆNH THƯỜNG DÙNG**

```bash
# Khởi động
docker-compose up -d

# Dừng
docker-compose down

# Xem logs
docker-compose logs -f

# Restart
docker-compose restart

# Rebuild
docker-compose up --build -d

# Reset hoàn toàn
docker-compose down -v && docker-compose up --build -d
```

---

## 🛠️ **XỬ LÝ LỖI NHANH**

### **❌ Port 3000 đã sử dụng**
```bash
# Thay đổi port trong docker-compose.yml
ports:
  - "3001:3000"  # Dùng port 3001
```

### **❌ Container không start**
```bash
# Xem lỗi
docker-compose logs backend

# Restart
docker-compose restart backend
```

### **❌ MongoDB lỗi**
```bash
# Restart MongoDB
docker-compose restart mongodb

# Xem logs
docker-compose logs mongodb
```

### **❌ Reset tất cả**
```bash
docker-compose down -v
docker system prune -a
docker-compose up --build -d
```

---

## 📊 **KIỂM TRA NHANH**

```bash
# Trạng thái containers
docker-compose ps

# Resource usage
docker stats

# Test API
curl http://localhost:3000/api/health
curl http://localhost:3000/api/products

# Logs realtime
docker-compose logs -f backend
```

---

## 🔒 **PRODUCTION CHECKLIST**

- [ ] Đổi mật khẩu trong `.env`:
  ```env
  MONGODB_URI=******************************************************************
  JWT_SECRET=your-strong-32-char-secret
  SESSION_SECRET=your-strong-32-char-secret
  ```

- [ ] Backup database:
  ```bash
  docker exec nafood-mongodb mongodump --uri="*******************************************************************" --out="/tmp/backup"
  docker cp nafood-mongodb:/tmp/backup ./backup
  ```

- [ ] Monitor logs:
  ```bash
  docker-compose logs -f
  ```

---

## 📞 **HỖ TRỢ NHANH**

**Gặp lỗi? Thử theo thứ tự:**

1. `docker-compose logs -f` - Xem lỗi
2. `docker-compose restart` - Restart services  
3. `docker-compose down && docker-compose up -d` - Restart hoàn toàn
4. `docker-compose down -v && docker-compose up --build -d` - Reset tất cả

**Cần help? Xem file `HUONG_DAN_CHAY_DOCKER.md` để có hướng dẫn chi tiết!**

---

## 🎉 **THÀNH CÔNG!**

**✅ Nếu thấy:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-24T...",
  "mongodb": "connected"
}
```

**🎊 Chúc mừng! Docker đã chạy thành công!**

**🔗 Truy cập ngay: http://localhost:3000/api/health**
