# Use official Node.js runtime as base image
FROM node:18-alpine

# Install system dependencies for bcrypt and other native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    curl

# Set working directory in container
WORKDIR /app

# Copy package files first for better Docker layer caching
COPY package*.json ./

# Install dependencies with better error handling
RUN npm ci --no-audit --no-fund && \
    npm cache clean --force

# Copy application code
COPY server ./server
COPY shared ./shared
COPY public ./public
COPY scripts ./scripts
COPY *.js ./
COPY tailwind.config.js ./
COPY postcss.config.js ./
COPY vite.config.js ./

# Create necessary directories
RUN mkdir -p public/uploads && \
    mkdir -p logs

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nafood -u 1001

# Change ownership of app directory
RUN chown -R nafood:nodejs /app && \
    chmod -R 755 /app

# Switch to non-root user
USER nafood

# Set environment variables
ENV NODE_ENV=production
ENV DOCKER_ENV=true
ENV PORT=3000

# Expose port
EXPOSE 3000

# Enhanced health check with better timeout and start period
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start application with proper signal handling
CMD ["npm", "run", "start:prod"]
