#!/usr/bin/env node

/**
 * Docker Setup Test Script
 * This script tests the complete Docker setup including API endpoints
 */

import http from 'http';
import { MongoClient } from 'mongodb';

const BASE_URL = 'http://localhost:3000';
const MONGODB_URI = process.env.MONGODB_URI || '*****************************************************************';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Make HTTP request
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = responseData ? JSON.parse(responseData) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test API endpoint
async function testEndpoint(method, path, expectedStatus = 200, data = null, headers = {}) {
  try {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const response = await makeRequest(options, data);
    
    if (response.statusCode === expectedStatus) {
      log(`✅ ${method} ${path} - Status: ${response.statusCode}`, colors.green);
      return { success: true, response };
    } else {
      log(`❌ ${method} ${path} - Expected: ${expectedStatus}, Got: ${response.statusCode}`, colors.red);
      return { success: false, response };
    }
  } catch (error) {
    log(`❌ ${method} ${path} - Error: ${error.message}`, colors.red);
    return { success: false, error: error.message };
  }
}

// Test user registration and login
async function testAuthFlow() {
  log('\n🔐 Testing Authentication Flow...', colors.cyan);
  
  const testUser = {
    username: `testuser_${Date.now()}`,
    email: `test_${Date.now()}@example.com`,
    password: 'testpassword123',
    fullName: 'Test User',
    phone: '0123456789'
  };

  // Test registration
  const registerResult = await testEndpoint('POST', '/api/auth/register', 201, testUser);
  if (!registerResult.success) {
    log('❌ Registration failed, skipping login test', colors.red);
    return null;
  }

  // Test login
  const loginResult = await testEndpoint('POST', '/api/auth/login', 200, {
    email: testUser.email,
    password: testUser.password
  });

  if (loginResult.success && loginResult.response.data.token) {
    log('✅ Authentication flow completed successfully', colors.green);
    return loginResult.response.data.token;
  } else {
    log('❌ Login failed', colors.red);
    return null;
  }
}

// Test protected endpoints
async function testProtectedEndpoints(token) {
  log('\n🔒 Testing Protected Endpoints...', colors.cyan);
  
  const authHeaders = { 'Authorization': `Bearer ${token}` };
  
  const endpoints = [
    { method: 'GET', path: '/api/auth/test', status: 200 },
    { method: 'GET', path: '/api/orders', status: 200 },
    { method: 'GET', path: '/api/products', status: 200 },
    { method: 'GET', path: '/api/reviews', status: 200 },
    { method: 'GET', path: '/api/banners', status: 200 }
  ];

  let successCount = 0;
  for (const endpoint of endpoints) {
    const result = await testEndpoint(
      endpoint.method, 
      endpoint.path, 
      endpoint.status, 
      null, 
      authHeaders
    );
    if (result.success) successCount++;
  }

  log(`✅ Protected endpoints: ${successCount}/${endpoints.length} passed`, 
      successCount === endpoints.length ? colors.green : colors.yellow);
}

// Test database operations
async function testDatabase() {
  log('\n🗄️  Testing Database Operations...', colors.cyan);
  
  let client;
  try {
    client = new MongoClient(MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
    });

    await client.connect();
    const db = client.db('nafood');

    // Test basic operations
    const testCollection = db.collection('test_collection');
    
    // Insert test document
    const insertResult = await testCollection.insertOne({
      test: true,
      timestamp: new Date(),
      data: 'Docker test'
    });
    
    // Find test document
    const findResult = await testCollection.findOne({ _id: insertResult.insertedId });
    
    // Delete test document
    await testCollection.deleteOne({ _id: insertResult.insertedId });
    
    if (findResult && findResult.test === true) {
      log('✅ Database operations: OK', colors.green);
      return true;
    } else {
      log('❌ Database operations: Failed', colors.red);
      return false;
    }
  } catch (error) {
    log(`❌ Database operations: ${error.message}`, colors.red);
    return false;
  } finally {
    if (client) {
      await client.close();
    }
  }
}

// Test file upload
async function testFileUpload(token) {
  log('\n📁 Testing File Upload...', colors.cyan);
  
  // Create a simple test file content (base64 encoded 1x1 pixel PNG)
  const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
  const testImageBuffer = Buffer.from(testImageBase64, 'base64');
  
  // Note: This is a simplified test. In a real scenario, you'd use multipart/form-data
  // For now, we'll just test that the upload endpoint is accessible
  const result = await testEndpoint('POST', '/api/upload', 400, null, {
    'Authorization': `Bearer ${token}`
  });
  
  // We expect 400 because we're not sending a proper multipart form
  // But this confirms the endpoint is accessible and requires auth
  if (result.response.statusCode === 400) {
    log('✅ File upload endpoint: Accessible (auth required)', colors.green);
    return true;
  } else {
    log('❌ File upload endpoint: Unexpected response', colors.red);
    return false;
  }
}

// Main test function
async function runTests() {
  log('🧪 Starting Docker Setup Tests...', colors.blue);
  log('==================================', colors.blue);

  const results = {
    timestamp: new Date().toISOString(),
    tests: {}
  };

  // Test basic endpoints
  log('\n🌐 Testing Basic Endpoints...', colors.cyan);
  const basicTests = [
    { method: 'GET', path: '/api/health', status: 200 },
    { method: 'GET', path: '/api/products', status: 200 },
    { method: 'GET', path: '/api/banners', status: 200 }
  ];

  let basicTestsPassed = 0;
  for (const test of basicTests) {
    const result = await testEndpoint(test.method, test.path, test.status);
    if (result.success) basicTestsPassed++;
  }
  results.tests.basicEndpoints = `${basicTestsPassed}/${basicTests.length}`;

  // Test database
  results.tests.database = await testDatabase();

  // Test authentication flow
  const token = await testAuthFlow();
  results.tests.authentication = !!token;

  if (token) {
    // Test protected endpoints
    await testProtectedEndpoints(token);
    
    // Test file upload
    results.tests.fileUpload = await testFileUpload(token);
  }

  // Summary
  log('\n==================================', colors.blue);
  log('📊 Test Results Summary:', colors.blue);
  log(`Basic Endpoints: ${results.tests.basicEndpoints}`, 
      basicTestsPassed === basicTests.length ? colors.green : colors.red);
  log(`Database: ${results.tests.database ? 'PASS' : 'FAIL'}`, 
      results.tests.database ? colors.green : colors.red);
  log(`Authentication: ${results.tests.authentication ? 'PASS' : 'FAIL'}`, 
      results.tests.authentication ? colors.green : colors.red);
  
  if (results.tests.fileUpload !== undefined) {
    log(`File Upload: ${results.tests.fileUpload ? 'PASS' : 'FAIL'}`, 
        results.tests.fileUpload ? colors.green : colors.red);
  }

  const allPassed = results.tests.database && results.tests.authentication && basicTestsPassed === basicTests.length;
  
  if (allPassed) {
    log('\n🎉 All tests passed! Docker setup is working correctly.', colors.green);
    process.exit(0);
  } else {
    log('\n💥 Some tests failed. Please check the Docker setup.', colors.red);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  // Wait a bit for the server to be ready
  setTimeout(() => {
    runTests().catch((error) => {
      log(`💥 Test execution failed: ${error.message}`, colors.red);
      process.exit(1);
    });
  }, 2000);
}

export { runTests };
