{"name": "htcuahangbandoan", "version": "2.0.0", "description": "<PERSON><PERSON> thống quản lý cửa hàng bán đồ ăn hiện đại", "type": "module", "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/Lamvanna/HTCUAHANGBANDOAN.git"}, "keywords": ["restaurant", "food", "management", "react", "nodejs", "mongodb"], "scripts": {"dev": "node start.js", "build": "vite build && esbuild server/index.js --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "node start.js", "start:prod": "node start-prod.js", "seed": "node scripts/seed-data.js", "create-test-orders": "node scripts/create-test-orders.js", "validate-env": "node scripts/validate-env.js", "prestart": "npm run validate-env"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-query": "^5.60.5", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "dotenv": "^17.2.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "mongodb": "^6.17.0", "mongoose": "^8.16.3", "multer": "^2.0.2", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "vite": "^5.4.19"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}