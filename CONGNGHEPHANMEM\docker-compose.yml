services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: nafood-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: nafood
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - nafood-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Backend API
  backend:
    build:
      context: ..
      dockerfile: CONGNGHEPHANMEM/Dockerfile
    container_name: nafood-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DOCKER_ENV: "true"
      PORT: 3000
      MONGODB_URI: *********************************************************************************************
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      SESSION_SECRET: your-session-secret-change-in-production
      FRONTEND_URL: http://localhost:3000
    ports:
      - "3000:3000"
    volumes:
      - ./public/uploads:/app/public/uploads
      - ./logs:/app/logs
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - nafood-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Redis for Session Store (Optional)
  redis:
    image: redis:7-alpine
    container_name: nafood-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - nafood-network
    command: redis-server --appendonly yes

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  nafood-network:
    driver: bridge
